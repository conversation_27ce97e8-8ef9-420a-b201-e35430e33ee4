import { useState, useMemo } from "react";

export const useFilteredData = (lineChartData, scatterPlotData) => {
  // Filter states
  const [selectedCompanies, setSelectedCompanies] = useState([]);
  const [dateFrom, setDateFrom] = useState(null);
  const [dateTo, setDateTo] = useState(null);
  const [selectedFuelType, setSelectedFuelType] = useState("all");
  const [horsepowerRange, setHorsepowerRange] = useState("all");

  // In-memory filtering functions
  const filteredLineChartData = useMemo(() => {
    if (!lineChartData.length) return [];

    let filtered = lineChartData;

    // Filter by companies
    if (selectedCompanies.length > 0) {
      filtered = filtered.filter((item) =>
        selectedCompanies.includes(item.company)
      );
    }

    // Filter by custom date range
    if (dateFrom || dateTo) {
      console.log("Date filtering:", {
        dateFrom,
        dateTo,
        originalLength: filtered.length,
      });

      filtered = filtered.filter((item) => {
        const itemDate = new Date(item.time);

        // Check if date is within the selected range
        let includeItem = true;

        if (dateFrom) {
          // Set dateFrom to start of day for comparison
          const fromDate = new Date(dateFrom);
          fromDate.setHours(0, 0, 0, 0);
          if (itemDate < fromDate) {
            includeItem = false;
          }
        }

        if (dateTo && includeItem) {
          // Set dateTo to end of day for comparison
          const toDate = new Date(dateTo);
          toDate.setHours(23, 59, 59, 999);
          if (itemDate > toDate) {
            includeItem = false;
          }
        }

        return includeItem;
      });

      console.log("After date filtering:", { filteredLength: filtered.length });
    }

    return filtered;
  }, [lineChartData, selectedCompanies, dateFrom, dateTo]);

  const filteredScatterPlotData = useMemo(() => {
    if (!scatterPlotData.length) return [];

    let filtered = scatterPlotData;

    // Filter by companies
    if (selectedCompanies.length > 0) {
      filtered = filtered.filter((item) =>
        selectedCompanies.includes(item.company)
      );
    }

    // Filter by fuel type
    if (selectedFuelType !== "all") {
      filtered = filtered.filter((item) => item.fuel_type === selectedFuelType);
    }

    // Filter by horsepower range
    if (horsepowerRange !== "all") {
      switch (horsepowerRange) {
        case "low":
          filtered = filtered.filter((item) => item.horsepower <= 100);
          break;
        case "medium":
          filtered = filtered.filter(
            (item) => item.horsepower > 100 && item.horsepower <= 200
          );
          break;
        case "high":
          filtered = filtered.filter((item) => item.horsepower > 200);
          break;
      }
    }

    return filtered;
  }, [scatterPlotData, selectedCompanies, selectedFuelType, horsepowerRange]);

  const resetFilters = () => {
    setSelectedCompanies([]);
    setDateFrom(null);
    setDateTo(null);
    setSelectedFuelType("all");
    setHorsepowerRange("all");
  };

  return {
    // Filter states
    selectedCompanies,
    setSelectedCompanies,
    dateFrom,
    setDateFrom,
    dateTo,
    setDateTo,
    selectedFuelType,
    setSelectedFuelType,
    horsepowerRange,
    setHorsepowerRange,

    // Filtered data (computed in-memory)
    filteredLineChartData,
    filteredScatterPlotData,

    // Actions
    resetFilters,
  };
};
