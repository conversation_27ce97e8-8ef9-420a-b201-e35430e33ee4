import { useState, useEffect } from "react";

export const useFilteredData = (lineChartData, scatterPlotData) => {
  // Filter states
  const [selectedCompanies, setSelectedCompanies] = useState([]);
  const [selectedDateRange, setSelectedDateRange] = useState("all");
  const [selectedFuelType, setSelectedFuelType] = useState("all");
  const [horsepowerRange, setHorsepowerRange] = useState("all");
  
  // Filtered data states (simulating backend responses)
  const [filteredLineChartData, setFilteredLineChartData] = useState([]);
  const [filteredScatterPlotData, setFilteredScatterPlotData] = useState([]);
  const [isFilterLoading, setIsFilterLoading] = useState(false);

  // Simulate backend API calls to fetch filtered data
  const fetchFilteredLineChartData = async (filters) => {
    try {
      setIsFilterLoading(true);

      // Simulate API delay (3 seconds)
      await new Promise((resolve) => setTimeout(resolve, 3000));

      // Simulate building query parameters
      const queryParams = new URLSearchParams();
      if (filters.companies && filters.companies.length > 0) {
        queryParams.append("companies", filters.companies.join(","));
      }
      if (filters.dateRange && filters.dateRange !== "all") {
        queryParams.append("dateRange", filters.dateRange);
      }

      console.log(
        `🔄 Simulated API call: /api/line-chart-data?${queryParams.toString()}`
      );

      // Fetch original data and apply filters (simulating backend filtering)
      const response = await fetch("/data/lineChart.json");
      if (!response.ok) {
        throw new Error("Failed to fetch filtered line chart data");
      }
      let data = await response.json();

      // Apply filters (simulating backend logic)
      if (filters.companies && filters.companies.length > 0) {
        data = data.filter((item) => filters.companies.includes(item.company));
      }

      if (filters.dateRange && filters.dateRange !== "all") {
        const now = new Date();
        const cutoffDate = new Date();

        switch (filters.dateRange) {
          case "last3months":
            cutoffDate.setMonth(now.getMonth() - 3);
            break;
          case "last6months":
            cutoffDate.setMonth(now.getMonth() - 6);
            break;
          case "lastyear":
            cutoffDate.setFullYear(now.getFullYear() - 1);
            break;
        }

        data = data.filter((item) => new Date(item.time) >= cutoffDate);
      }

      return data;
    } catch (err) {
      throw new Error(`Filtered line chart data error: ${err.message}`);
    } finally {
      setIsFilterLoading(false);
    }
  };

  const fetchFilteredScatterPlotData = async (filters) => {
    try {
      setIsFilterLoading(true);

      // Simulate API delay (3 seconds)
      await new Promise((resolve) => setTimeout(resolve, 3000));

      // Simulate building query parameters
      const queryParams = new URLSearchParams();
      if (filters.companies && filters.companies.length > 0) {
        queryParams.append("companies", filters.companies.join(","));
      }
      if (filters.fuelType && filters.fuelType !== "all") {
        queryParams.append("fuelType", filters.fuelType);
      }
      if (filters.horsepowerRange && filters.horsepowerRange !== "all") {
        queryParams.append("horsepowerRange", filters.horsepowerRange);
      }

      console.log(
        `🔄 Simulated API call: /api/scatter-plot-data?${queryParams.toString()}`
      );

      // Fetch original data and apply filters (simulating backend filtering)
      const response = await fetch("/data/scatterPlot.json");
      if (!response.ok) {
        throw new Error("Failed to fetch filtered scatter plot data");
      }
      let data = await response.json();

      // Apply filters (simulating backend logic)
      if (filters.companies && filters.companies.length > 0) {
        data = data.filter((item) => filters.companies.includes(item.company));
      }

      if (filters.fuelType && filters.fuelType !== "all") {
        data = data.filter((item) => item.fuel_type === filters.fuelType);
      }

      if (filters.horsepowerRange && filters.horsepowerRange !== "all") {
        switch (filters.horsepowerRange) {
          case "low":
            data = data.filter((item) => item.horsepower <= 100);
            break;
          case "medium":
            data = data.filter(
              (item) => item.horsepower > 100 && item.horsepower <= 200
            );
            break;
          case "high":
            data = data.filter((item) => item.horsepower > 200);
            break;
        }
      }

      return data;
    } catch (err) {
      throw new Error(`Filtered scatter plot data error: ${err.message}`);
    } finally {
      setIsFilterLoading(false);
    }
  };

  // Initialize filtered data with all data when original data loads
  useEffect(() => {
    if (lineChartData.length > 0) {
      setFilteredLineChartData(lineChartData);
    }
    if (scatterPlotData.length > 0) {
      setFilteredScatterPlotData(scatterPlotData);
    }
  }, [lineChartData, scatterPlotData]);

  // Fetch filtered data whenever filters change (simulating backend calls)
  useEffect(() => {
    const fetchFilteredData = async () => {
      try {
        const filters = {
          companies: selectedCompanies,
          dateRange: selectedDateRange,
          fuelType: selectedFuelType,
          horsepowerRange: horsepowerRange,
        };

        console.log("🔄 Filter changed, making new API calls...", filters);

        // Only fetch if we have initial data
        if (lineChartData.length > 0 || scatterPlotData.length > 0) {
          const [filteredLineData, filteredScatterData] = await Promise.all([
            fetchFilteredLineChartData(filters),
            fetchFilteredScatterPlotData(filters),
          ]);

          setFilteredLineChartData(filteredLineData);
          setFilteredScatterPlotData(filteredScatterData);
        }
      } catch (err) {
        console.error("Error fetching filtered data:", err);
      }
    };

    // Don't fetch on initial render (handled by first useEffect)
    if (lineChartData.length > 0 || scatterPlotData.length > 0) {
      fetchFilteredData();
    }
  }, [selectedCompanies, selectedDateRange, selectedFuelType, horsepowerRange]);

  const resetFilters = () => {
    setSelectedCompanies([]);
    setSelectedDateRange("all");
    setSelectedFuelType("all");
    setHorsepowerRange("all");
  };

  return {
    // Filter states
    selectedCompanies,
    setSelectedCompanies,
    selectedDateRange,
    setSelectedDateRange,
    selectedFuelType,
    setSelectedFuelType,
    horsepowerRange,
    setHorsepowerRange,
    
    // Filtered data
    filteredLineChartData,
    filteredScatterPlotData,
    isFilterLoading,
    
    // Actions
    resetFilters,
  };
};
