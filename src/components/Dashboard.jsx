import { useDataFetching } from "../hooks/useDataFetching";
import { useFilteredData } from "../hooks/useFilteredData";
import { useChartData } from "../hooks/useChartData";
import { FilterPanel } from "./FilterPanel";
import { PriceLineChart } from "./PriceLineChart";
import { CarPerformanceScatterChart } from "./CarPerformanceScatterChart";
import { SimpleMap } from "./SimpleMap";

export const Dashboard = () => {
  // Fetch initial data
  const { lineChartData, scatterPlotData, loading, error } = useDataFetching();

  console.log("Dashboard render:", {
    loading,
    error,
    lineChartDataLength: lineChartData.length,
  });

  // Handle filtered data and filter states
  const {
    selectedCompanies,
    setSelectedCompanies,
    dateFrom,
    setDateFrom,
    dateTo,
    setDateTo,
    selectedFuelType,
    setSelectedFuelType,
    horsepowerRange,
    setHorsepowerRange,
    filteredLineChartData,
    filteredScatterPlotData,
    resetFilters,
  } = useFilteredData(lineChartData, scatterPlotData);

  // Process data for charts
  const {
    mantineLineData,
    mantineScatterData,
    companies,
    allFuelTypes,
    getCompanyColor,
  } = useChartData(
    filteredLineChartData,
    filteredScatterPlotData,
    lineChartData,
    scatterPlotData
  );

  if (loading) {
    return <div>Loading data...</div>;
  }

  if (error) {
    return <div>Error: {error}</div>;
  }

  return (
    <div className="app">
      <h1>Car Rental Analytics Dashboard</h1>

      <FilterPanel
        companies={companies}
        allFuelTypes={allFuelTypes}
        selectedCompanies={selectedCompanies}
        setSelectedCompanies={setSelectedCompanies}
        dateFrom={dateFrom}
        setDateFrom={setDateFrom}
        dateTo={dateTo}
        setDateTo={setDateTo}
        selectedFuelType={selectedFuelType}
        setSelectedFuelType={setSelectedFuelType}
        horsepowerRange={horsepowerRange}
        setHorsepowerRange={setHorsepowerRange}
        resetFilters={resetFilters}
        mantineLineData={mantineLineData}
        mantineScatterData={mantineScatterData}
      />

      <PriceLineChart
        mantineLineData={mantineLineData}
        getCompanyColor={getCompanyColor}
      />

      <br />
      <br />

      <CarPerformanceScatterChart mantineScatterData={mantineScatterData} />

      <br />
      <br />

      <SimpleMap />
    </div>
  );
};
