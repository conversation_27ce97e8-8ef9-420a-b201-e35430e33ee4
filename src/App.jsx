import "@mantine/core/styles.css";
import "@mantine/charts/styles.css";
import "./App.css";
import { useState, useEffect } from "react";
import { Line<PERSON><PERSON>, ScatterChart } from "@mantine/charts";

const App = () => {
  const [lineChartData, setLineChartData] = useState([]);
  const [scatterPlotData, setScatterPlotData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const [isAnimated, setIsAnimated] = useState(false);

  // Simulate backend API calls to fetch JSON data
  const fetchLineChartData = async () => {
    try {
      const response = await fetch("/data/lineChart.json");
      if (!response.ok) {
        throw new Error("Failed to fetch line chart data");
      }
      const data = await response.json();
      return data;
    } catch (err) {
      throw new Error(`Line chart data error: ${err.message}`);
    }
  };

  const fetchScatterPlotData = async () => {
    try {
      const response = await fetch("/data/scatterPlot.json");
      if (!response.ok) {
        throw new Error("Failed to fetch scatter plot data");
      }
      const data = await response.json();
      return data;
    } catch (err) {
      throw new Error(`Scatter plot data error: ${err.message}`);
    }
  };

  // Fetch both datasets on component mount
  useEffect(() => {
    const fetchAllData = async () => {
      try {
        setLoading(true);
        const [lineData, scatterData] = await Promise.all([
          fetchLineChartData(),
          fetchScatterPlotData(),
        ]);

        setTimeout(() => {
          setLineChartData(lineData);
          setScatterPlotData(scatterData);
        }, 3000);
        setError(null);
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchAllData();
  }, []);

  if (loading) {
    return <div>Loading data...</div>;
  }

  if (error) {
    return <div>Error: {error}</div>;
  }

  // Process line chart data for Mantine
  const processMantineLineData = () => {
    if (!lineChartData.length) return [];

    // Get unique sorted dates
    const uniqueDates = [
      ...new Set(lineChartData.map((item) => item.time)),
    ].sort((a, b) => new Date(a) - new Date(b));

    // Transform data for Mantine LineChart
    return uniqueDates.map((date) => {
      const dataPoint = { date: new Date(date).toLocaleDateString() };

      // Add price for each company at this date
      const companies = [...new Set(lineChartData.map((item) => item.company))];

      companies.forEach((company) => {
        const companyData = lineChartData.find(
          (item) => item.company === company && item.time === date
        );
        dataPoint[company] = companyData ? companyData.price : null;
      });

      return dataPoint;
    });
  };

  // Process scatter plot data for Mantine
  const processMantineScatterData = () => {
    if (!scatterPlotData.length) return [];

    return scatterPlotData.map((item) => ({
      horsepower: item.horsepower,
      range: item.range,
      company: item.company,
      name: item.name,
    }));
  };

  const mantineLineData = lineChartData ? processMantineLineData() : [];
  // console.log("mantineLineData", mantineLineData);

  const mantineScatterData = processMantineScatterData();
  console.log("mantineScatterData", mantineScatterData);

  const companies = [...new Set(lineChartData.map((item) => item.company))];
  // console.log("companies", companies);

  // Helper function to get consistent colors for companies
  const getCompanyColor = (company) => {
    const colorMap = {
      Avis: "#FF6384",
      FlexCar: "#36A2EB",
      Hertz: "#FFCE56",
      Instacar: "#4BC0C0",
      Sixt: "#9966FF",
    };
    return colorMap[company] || "#999999";
  };

  const data = [
    {
      color: "blue.5",
      name: "Group 1",
      data: [
        { age: 25, BMI: 20 },
        { age: 30, BMI: 22 },
        { age: 35, BMI: 18 },
        { age: 40, BMI: 25 },
        { age: 45, BMI: 30 },
        { age: 28, BMI: 15 },
        { age: 22, BMI: 12 },
        { age: 50, BMI: 28 },
        { age: 32, BMI: 19 },
        { age: 48, BMI: 31 },
        { age: 26, BMI: 24 },
        { age: 38, BMI: 27 },
        { age: 42, BMI: 29 },
        { age: 29, BMI: 16 },
        { age: 34, BMI: 23 },
        { age: 44, BMI: 33 },
        { age: 23, BMI: 14 },
        { age: 37, BMI: 26 },
        { age: 49, BMI: 34 },
        { age: 27, BMI: 17 },
        { age: 41, BMI: 32 },
        { age: 31, BMI: 21 },
        { age: 46, BMI: 35 },
        { age: 24, BMI: 13 },
        { age: 33, BMI: 22 },
        { age: 39, BMI: 28 },
        { age: 47, BMI: 30 },
        { age: 36, BMI: 25 },
        { age: 43, BMI: 29 },
        { age: 21, BMI: 11 },
      ],
    },
  ];

  console

  return (
    <div className="app">
      <h1>Car Rental Analytics Dashboard</h1>

      <div style={{ marginBottom: "40px" }}>
        <h2>Price Trends Over Time (Mantine)</h2>

        <div
          style={{
            "--line-animation": "draw-line 2s ease-out forwards",
          }}
          onAnimationEnd={() => setIsAnimated(true)}
        >
          <style jsx>{`
            @keyframes draw-line {
              from {
                stroke-dasharray: 1000;
                stroke-dashoffset: 1000;
              }
              to {
                stroke-dasharray: 1000;
                stroke-dashoffset: 0;
              }
            }
            .recharts-line-curve {
              animation: var(--line-animation);
            }
          `}</style>

          <LineChart
            h={400}
            data={mantineLineData}
            dataKey="date"
            series={companies.map((company) => ({
              name: company,
              color: getCompanyColor(company),
            }))}
            curveType="linear"
            withLegend
            withTooltip
            withDots={isAnimated}
          />
        </div>
      </div>

      <div>
        <h2>Car Performance Analysis (Mantine)</h2>

        {mantineScatterData.length > 0 ? (
          <ScatterChart
            h={400}
            data={data}
            dataKey={{ x: "horsepower", y: "range" }}
            withLegend
            withTooltip
            xAxisLabel="Horsepower"
            yAxisLabel="Range (km)"
          />
        ) : (
          <p>Loading scatter plot...</p>
        )}
      </div>
    </div>
  );
};

export default App;
