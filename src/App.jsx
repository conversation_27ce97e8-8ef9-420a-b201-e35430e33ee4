import "@mantine/core/styles.css";
import "@mantine/charts/styles.css";
import "./App.css";
import { useState, useEffect } from "react";
import { Line<PERSON><PERSON>, Scatter<PERSON>hart } from "@mantine/charts";
import {
  MultiSelect,
  Select,
  Group,
  Text,
  Paper,
  Stack,
  Button,
} from "@mantine/core";

const App = () => {
  const [lineChartData, setLineChartData] = useState([]);
  const [scatterPlotData, setScatterPlotData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Filter states
  const [selectedCompanies, setSelectedCompanies] = useState([]);
  const [selectedDateRange, setSelectedDateRange] = useState("all");
  const [selectedFuelType, setSelectedFuelType] = useState("all");
  const [horsepowerRange, setHorsepowerRange] = useState("all");

  const [isAnimated, setIsAnimated] = useState(false);

  // Simulate backend API calls to fetch JSON data
  const fetchLineChartData = async () => {
    try {
      const response = await fetch("/data/lineChart.json");
      if (!response.ok) {
        throw new Error("Failed to fetch line chart data");
      }
      const data = await response.json();
      return data;
    } catch (err) {
      throw new Error(`Line chart data error: ${err.message}`);
    }
  };

  const fetchScatterPlotData = async () => {
    try {
      const response = await fetch("/data/scatterPlot.json");
      if (!response.ok) {
        throw new Error("Failed to fetch scatter plot data");
      }
      const data = await response.json();
      return data;
    } catch (err) {
      throw new Error(`Scatter plot data error: ${err.message}`);
    }
  };

  // Fetch both datasets on component mount
  useEffect(() => {
    const fetchAllData = async () => {
      try {
        setLoading(true);
        const [lineData, scatterData] = await Promise.all([
          fetchLineChartData(),
          fetchScatterPlotData(),
        ]);

        setTimeout(() => {
          setLineChartData(lineData);
          setScatterPlotData(scatterData);
        }, 3000);
        setError(null);
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchAllData();
  }, []);

  if (loading) {
    return <div>Loading data...</div>;
  }

  if (error) {
    return <div>Error: {error}</div>;
  }

  // Helper function to get consistent colors for companies
  const getCompanyColor = (company) => {
    const colorMap = {
      Avis: "#FF6384",
      FlexCar: "#36A2EB",
      Hertz: "#FFCE56",
      Instacar: "#4BC0C0",
      Sixt: "#9966FF",
    };
    return colorMap[company] || "#999999";
  };

  // Filter functions
  const filterLineChartData = () => {
    if (!lineChartData.length) return [];

    let filtered = lineChartData;

    // Filter by companies
    if (selectedCompanies.length > 0) {
      filtered = filtered.filter((item) =>
        selectedCompanies.includes(item.company)
      );
    }

    // Filter by date range
    if (selectedDateRange !== "all") {
      const now = new Date();
      const cutoffDate = new Date();

      switch (selectedDateRange) {
        case "last3months":
          cutoffDate.setMonth(now.getMonth() - 3);
          break;
        case "last6months":
          cutoffDate.setMonth(now.getMonth() - 6);
          break;
        case "lastyear":
          cutoffDate.setFullYear(now.getFullYear() - 1);
          break;
      }

      filtered = filtered.filter((item) => new Date(item.time) >= cutoffDate);
    }

    return filtered;
  };

  const filterScatterPlotData = () => {
    if (!scatterPlotData.length) return [];

    let filtered = scatterPlotData;

    // Filter by companies
    if (selectedCompanies.length > 0) {
      filtered = filtered.filter((item) =>
        selectedCompanies.includes(item.company)
      );
    }

    // Filter by fuel type
    if (selectedFuelType !== "all") {
      filtered = filtered.filter((item) => item.fuel_type === selectedFuelType);
    }

    // Filter by horsepower range
    if (horsepowerRange !== "all") {
      switch (horsepowerRange) {
        case "low":
          filtered = filtered.filter((item) => item.horsepower <= 100);
          break;
        case "medium":
          filtered = filtered.filter(
            (item) => item.horsepower > 100 && item.horsepower <= 200
          );
          break;
        case "high":
          filtered = filtered.filter((item) => item.horsepower > 200);
          break;
      }
    }

    return filtered;
  };

  // Apply filters and process data with error handling
  let filteredLineData = [];
  let filteredScatterData = [];
  let mantineLineData = [];
  let mantineScatterData = [];
  let companies = [];
  let allFuelTypes = [];

  try {
    filteredLineData = filterLineChartData();
    filteredScatterData = filterScatterPlotData();

    mantineLineData = filteredLineData.length
      ? processMantineLineDataWithFiltered(filteredLineData)
      : [];
    mantineScatterData = filteredScatterData.length
      ? processMantineScatterDataWithFiltered(filteredScatterData)
      : [];

    companies = [...new Set(lineChartData.map((item) => item.company))];
    allFuelTypes = [...new Set(scatterPlotData.map((item) => item.fuel_type))];
  } catch (err) {
    console.error("Error processing data:", err);
    setError("Error processing chart data: " + err.message);
  }

  // Helper functions for filtered data
  function processMantineLineDataWithFiltered(data) {
    if (!data || data.length === 0) return [];

    const uniqueDates = [...new Set(data.map((item) => item.time))].sort(
      (a, b) => new Date(a) - new Date(b)
    );

    // Get companies that actually exist in the filtered data
    const companiesInData = [...new Set(data.map((item) => item.company))];

    return uniqueDates.map((date) => {
      const dataPoint = { date: new Date(date).toLocaleDateString() };

      companiesInData.forEach((company) => {
        const companyData = data.find(
          (item) => item.company === company && item.time === date
        );
        dataPoint[company] = companyData ? companyData.price : null;
      });

      return dataPoint;
    });
  }

  function processMantineScatterDataWithFiltered(data) {
    const activeCompanies = [...new Set(data.map((item) => item.company))];

    return activeCompanies.map((company) => {
      const companyData = data
        .filter((item) => item.company === company)
        .map((item) => ({
          horsepower: item.horsepower,
          range: item.range,
        }));

      return {
        color: getCompanyColor(company),
        name: company,
        data: companyData,
      };
    });
  }

  const resetFilters = () => {
    setSelectedCompanies([]);
    setSelectedDateRange("all");
    setSelectedFuelType("all");
    setHorsepowerRange("all");
  };

  return (
    <div className="app">
      <h1>Car Rental Analytics Dashboard</h1>

      {/* Filters Section */}
      <Paper p="md" mb="xl" withBorder>
        <Text size="lg" fw={600} mb="md">
          Filters
        </Text>

        <Stack gap="md">
          <Group grow>
            <MultiSelect
              label="Companies"
              placeholder="Select companies"
              data={companies}
              value={selectedCompanies}
              onChange={setSelectedCompanies}
              clearable
            />

            <Select
              label="Date Range"
              placeholder="Select date range"
              data={[
                { value: "all", label: "All Time" },
                { value: "last3months", label: "Last 3 Months" },
                { value: "last6months", label: "Last 6 Months" },
                { value: "lastyear", label: "Last Year" },
              ]}
              value={selectedDateRange}
              onChange={setSelectedDateRange}
            />
          </Group>

          <Group grow>
            <Select
              label="Fuel Type"
              placeholder="Select fuel type"
              data={[
                { value: "all", label: "All Fuel Types" },
                ...allFuelTypes.map((type) => ({ value: type, label: type })),
              ]}
              value={selectedFuelType}
              onChange={setSelectedFuelType}
            />
            <Select
              label="Horsepower Range"
              placeholder="Select horsepower range"
              data={[
                { value: "all", label: "All Ranges" },
                { value: "low", label: "Low (≤100 HP)" },
                { value: "medium", label: "Medium (101-200 HP)" },
                { value: "high", label: "High (>200 HP)" },
              ]}
              value={horsepowerRange}
              onChange={setHorsepowerRange}
            />
          </Group>

          <Group>
            <Button variant="outline" onClick={resetFilters}>
              Reset All Filters
            </Button>
            <Text size="sm" c="dimmed">
              Showing {mantineLineData.length} time periods and{" "}
              {mantineScatterData.reduce(
                (acc, series) => acc + series.data.length,
                0
              )}{" "}
              cars
            </Text>
          </Group>
        </Stack>
      </Paper>

      {/* Debug Information */}
      {/* <Paper p="md" mb="xl" withBorder>
        <Text size="lg" fw={600} mb="md">
          Debug Information
        </Text>
        <Stack gap="sm">
          <Text size="sm">Loading: {loading.toString()}</Text>
          <Text size="sm">Error: {error || "None"}</Text>
          <Text size="sm">Line Chart Data Length: {lineChartData.length}</Text>
          <Text size="sm">
            Scatter Plot Data Length: {scatterPlotData.length}
          </Text>
          <Text size="sm">
            Filtered Line Data Length: {filteredLineData.length}
          </Text>
          <Text size="sm">
            Filtered Scatter Data Length: {filteredScatterData.length}
          </Text>
          <Text size="sm">
            Mantine Line Data Length: {mantineLineData.length}
          </Text>
          <Text size="sm">
            Mantine Scatter Data Length: {mantineScatterData.length}
          </Text>
          <Text size="sm">Companies: {companies.join(", ")}</Text>
          <Text size="sm">All Fuel Types: {allFuelTypes.join(", ")}</Text>
          <Text size="sm">
            Selected Companies: {selectedCompanies.join(", ") || "None"}
          </Text>
          <Text size="sm">Selected Date Range: {selectedDateRange}</Text>
          <Text size="sm">Selected Fuel Type: {selectedFuelType}</Text>
          <Text size="sm">Selected Horsepower Range: {horsepowerRange}</Text>
        </Stack>
      </Paper> */}

      <div style={{ marginBottom: "40px" }}>
        <h2>Price Trends Over Time (Mantine)</h2>

        <div
          style={{
            "--line-animation": "draw-line 2s ease-out forwards",
          }}
          onAnimationEnd={() => setIsAnimated(true)}
        >
          <style jsx>{`
            @keyframes draw-line {
              from {
                stroke-dasharray: 1000;
                stroke-dashoffset: 1000;
              }
              to {
                stroke-dasharray: 1000;
                stroke-dashoffset: 0;
              }
            }
            .recharts-line-curve {
              animation: var(--line-animation);
            }
          `}</style>

          <LineChart
            h={400}
            data={mantineLineData}
            dataKey="date"
            series={(() => {
              // Get companies that actually exist in the current data
              const companiesInData =
                mantineLineData.length > 0
                  ? Object.keys(mantineLineData[0]).filter(
                      (key) => key !== "date"
                    )
                  : [];
              return companiesInData.map((company) => ({
                name: company,
                color: getCompanyColor(company),
              }));
            })()}
            curveType="linear"
            withLegend
            withTooltip
            withDots={isAnimated}
          />
        </div>
      </div>

      <br />

      <br />

      <div>
        <h2>Car Performance Analysis (Mantine)</h2>

        {mantineScatterData.length > 0 ? (
          <ScatterChart
            h={400}
            data={mantineScatterData}
            dataKey={{ x: "horsepower", y: "range" }}
            withLegend
            withTooltip
            xAxisLabel="Horsepower"
            yAxisLabel="Range (km)"
          />
        ) : (
          <p>Loading scatter plot...</p>
        )}
      </div>
    </div>
  );
};

export default App;
