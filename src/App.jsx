import { useState, useEffect } from "react";
// Chart.js imports (commented out)
// import {
//   Chart as ChartJS,
//   CategoryScale,
//   LinearScale,
//   PointElement,
//   LineElement,
//   Title,
//   Tooltip,
//   Legend,
//   TimeScale,
// } from "chart.js";
// import { Line, Scatter } from "react-chartjs-2";
// import "chartjs-adapter-date-fns";

// Mantine imports
import { MantineProvider } from "@mantine/core";
import { LineChart, ScatterChart } from "@mantine/charts";
import "@mantine/core/styles.css";
import "@mantine/charts/styles.css";
import "./App.css";

// Register Chart.js components (commented out)
// ChartJS.register(
//   CategoryScale,
//   LinearScale,
//   PointElement,
//   LineElement,
//   Title,
//   Tooltip,
//   Legend,
//   TimeScale
// );

const App = () => {
  const [lineChartData, setLineChartData] = useState([]);
  const [scatterPlotData, setScatterPlotData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Simulate backend API calls to fetch JSON data
  const fetchLineChartData = async () => {
    try {
      const response = await fetch("/data/lineChart.json");
      if (!response.ok) {
        throw new Error("Failed to fetch line chart data");
      }
      const data = await response.json();
      return data;
    } catch (err) {
      throw new Error(`Line chart data error: ${err.message}`);
    }
  };

  const fetchScatterPlotData = async () => {
    try {
      const response = await fetch("/data/scatterPlot.json");
      if (!response.ok) {
        throw new Error("Failed to fetch scatter plot data");
      }
      const data = await response.json();
      return data;
    } catch (err) {
      throw new Error(`Scatter plot data error: ${err.message}`);
    }
  };

  // Fetch both datasets on component mount
  useEffect(() => {
    const fetchAllData = async () => {
      try {
        setLoading(true);
        const [lineData, scatterData] = await Promise.all([
          fetchLineChartData(),
          fetchScatterPlotData(),
        ]);

        setLineChartData(lineData);
        setScatterPlotData(scatterData);
        setError(null);
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchAllData();
  }, []);

  if (loading) {
    return <div>Loading data...</div>;
  }

  if (error) {
    return <div>Error: {error}</div>;
  }

  // Process line chart data for Chart.js
  // const processLineChartData = () => {
  //   if (!lineChartData.length) return null;

  //   // Group data by company
  //   const companies = [...new Set(lineChartData.map((item) => item.company))];
  //   const colors = ["#FF6384", "#36A2EB", "#FFCE56", "#4BC0C0", "#9966FF"];

  //   // Get unique sorted dates for labels
  //   const uniqueDates = [
  //     ...new Set(lineChartData.map((item) => item.time)),
  //   ].sort((a, b) => new Date(a) - new Date(b));

  //   const datasets = companies.map((company, index) => {
  //     const companyData = uniqueDates.map((date) => {
  //       const dataPoint = lineChartData.find(
  //         (item) => item.company === company && item.time === date
  //       );
  //       return dataPoint ? dataPoint.price : null;
  //     });

  //     return {
  //       label: company,
  //       data: companyData,
  //       borderColor: colors[index % colors.length],
  //       backgroundColor: colors[index % colors.length] + "20",
  //       tension: 0.1,
  //       spanGaps: true,
  //     };
  //   });

  //   return {
  //     labels: uniqueDates.map((date) => new Date(date).toLocaleDateString()),
  //     datasets,
  //   };
  // };

  // Process scatter plot data for Chart.js
  // const processScatterPlotData = () => {
  //   if (!scatterPlotData.length) return null;

  //   // Group data by company
  //   const companies = [...new Set(scatterPlotData.map((item) => item.company))];
  //   const colors = ["#FF6384", "#36A2EB", "#FFCE56", "#4BC0C0", "#9966FF"];

  //   const datasets = companies.map((company, index) => {
  //     const companyData = scatterPlotData
  //       .filter((item) => item.company === company)
  //       .map((item) => ({
  //         x: item.horsepower,
  //         y: item.range,
  //       }));

  //     return {
  //       label: company,
  //       data: companyData,
  //       backgroundColor: colors[index % colors.length],
  //       borderColor: colors[index % colors.length],
  //       pointRadius: 6,
  //     };
  //   });

  //   return {
  //     datasets,
  //   };
  // };

  // const lineOptions = {
  //   responsive: true,
  //   maintainAspectRatio: false,
  //   plugins: {
  //     legend: {
  //       position: "top",
  //     },
  //     title: {
  //       display: true,
  //       text: "Car Rental Prices Over Time by Company",
  //     },
  //   },
  //   scales: {
  //     x: {
  //       title: {
  //         display: true,
  //         text: "Date",
  //       },
  //     },
  //     y: {
  //       title: {
  //         display: true,
  //         text: "Price ($)",
  //       },
  //     },
  //   },
  // };

  // const scatterOptions = {
  //   responsive: true,
  //   maintainAspectRatio: false,
  //   plugins: {
  //     legend: {
  //       position: "top",
  //     },
  //     title: {
  //       display: true,
  //       text: "Car Horsepower vs Range by Company",
  //     },
  //   },
  //   scales: {
  //     x: {
  //       title: {
  //         display: true,
  //         text: "Horsepower",
  //       },
  //     },
  //     y: {
  //       title: {
  //         display: true,
  //         text: "Range (km)",
  //       },
  //     },
  //   },
  // };

  // const lineData = processLineChartData();
  // const scatterData = processScatterPlotData();

  // Process line chart data for Mantine
  const processMantineLineData = () => {
    if (!lineChartData.length) return [];

    // Get unique sorted dates
    const uniqueDates = [
      ...new Set(lineChartData.map((item) => item.time)),
    ].sort((a, b) => new Date(a) - new Date(b));

    // Transform data for Mantine LineChart
    return uniqueDates.map((date) => {
      const dataPoint = { date: new Date(date).toLocaleDateString() };

      // Add price for each company at this date
      const companies = [...new Set(lineChartData.map((item) => item.company))];

      companies.forEach((company) => {
        const companyData = lineChartData.find(
          (item) => item.company === company && item.time === date
        );
        dataPoint[company] = companyData ? companyData.price : null;
      });

      return dataPoint;
    });
  };

  // Process scatter plot data for Mantine
  const processMantineScatterData = () => {
    if (!scatterPlotData.length) return [];

    return scatterPlotData.map((item) => ({
      horsepower: item.horsepower,
      range: item.range,
      company: item.company,
      name: item.name,
    }));
  };

  const mantineLineData = processMantineLineData();
  const mantineScatterData = processMantineScatterData();
  const companies = [...new Set(lineChartData.map((item) => item.company))];

  // Helper function to get consistent colors for companies
  const getCompanyColor = (company) => {
    const colorMap = {
      Avis: "#FF6384",
      FlexCar: "#36A2EB",
      Hertz: "#FFCE56",
      Instacar: "#4BC0C0",
      Sixt: "#9966FF",
    };
    return colorMap[company] || "#999999";
  };

  return (
    <div className="app">
      <h1>Car Rental Analytics Dashboard</h1>

      {/* Chart.js implementation (commented out) */}
      {/* <div style={{ marginBottom: "40px" }}>
          <h2>Price Trends Over Time</h2>
          {lineData ? (
            <div style={{ height: "400px", marginBottom: "20px" }}>
              <Line data={lineData} options={lineOptions} />
            </div>
          ) : (
            <p>Loading line chart...</p>
          )}
        </div>

        <div>
          <h2>Car Performance Analysis</h2>
          {scatterData ? (
            <div style={{ height: "400px" }}>
              <Scatter data={scatterData} options={scatterOptions} />
            </div>
          ) : (
            <p>Loading scatter plot...</p>
          )}
        </div> */}

      {/* Mantine Charts Implementation */}
      <div style={{ marginBottom: "40px" }}>
        <h2>Price Trends Over Time (Mantine)</h2>
        {mantineLineData.length > 0 ? (
          <LineChart
            h={400}
            data={mantineLineData}
            dataKey="date"
            series={companies.map((company) => ({
              name: company,
              color: getCompanyColor(company),
            }))}
            curveType="linear"
            withLegend
            withTooltip
            withDots
          />
        ) : (
          <p>Loading line chart...</p>
        )}
      </div>

      <div>
        <h2>Car Performance Analysis (Mantine)</h2>
        {mantineScatterData.length > 0 ? (
          <ScatterChart
            h={400}
            data={mantineScatterData}
            dataKey={{ x: "horsepower", y: "range" }}
            withLegend
            withTooltip
            xAxisLabel="Horsepower"
            yAxisLabel="Range (km)"
          />
        ) : (
          <p>Loading scatter plot...</p>
        )}
      </div>
    </div>
  );
};

export default App;
