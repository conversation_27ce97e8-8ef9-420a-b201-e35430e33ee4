import { useState } from "react";
import { LineChart } from "@mantine/charts";

export const PriceLineChart = ({ mantineLineData, getCompanyColor }) => {
  const [isAnimated, setIsAnimated] = useState(false);

  return (
    <div>
      <h2>Price Trends Over Time</h2>

      <div
        style={{
          "--line-animation": "draw-line 2s ease-out forwards",
        }}
        onAnimationEnd={() => setIsAnimated(true)}
      >
        <style jsx>{`
          @keyframes draw-line {
            from {
              stroke-dasharray: 1000;
              stroke-dashoffset: 1000;
            }
            to {
              stroke-dasharray: 1000;
              stroke-dashoffset: 0;
            }
          }
          .recharts-line-curve {
            animation: var(--line-animation);
          }
        `}</style>

        <LineChart
          h={400}
          data={mantineLineData}
          dataKey="date"
          series={(() => {
            // Get companies that actually exist in the current data
            const companiesInData =
              mantineLineData.length > 0
                ? Object.keys(mantineLineData[0]).filter(
                    (key) => key !== "date"
                  )
                : [];
            return companiesInData.map((company) => ({
              name: company,
              color: getCompanyColor(company),
            }));
          })()}
          curveType="linear"
          withLegend
          withTooltip
          withDots={isAnimated}
          tooltipAnimationDuration={300}
        />
      </div>
    </div>
  );
};
