import { useDataFetching } from "../hooks/useDataFetching";
import { useFilteredData } from "../hooks/useFilteredData";
import { useChartData } from "../hooks/useChartData";
import { FilterPanel } from "./FilterPanel";
import { PriceLineChart } from "./PriceLineChart";
import { CarPerformanceScatterChart } from "./CarPerformanceScatterChart";
import { SimpleMap } from "./SimpleMap";

export const Dashboard = () => {
  // Fetch initial data
  const { lineChartData, scatterPlotData, loading, error } = useDataFetching();

  console.log("Dashboard render:", {
    loading,
    error,
    lineChartDataLength: lineChartData.length,
  });

  // Handle filtered data and filter states
  const {
    selectedCompanies,
    setSelectedCompanies,
    dateFrom,
    setDateFrom,
    dateTo,
    setDateTo,
    selectedFuelType,
    setSelectedFuelType,
    horsepowerRange,
    setHorsepowerRange,
    filteredLineChartData,
    filteredScatterPlotData,
    resetFilters,
  } = useFilteredData(lineChartData, scatterPlotData);

  // Process data for charts
  const {
    mantineLineData,
    mantineScatterData,
    companies,
    allFuelTypes,
    getCompanyColor,
  } = useChartData(
    filteredLineChartData,
    filteredScatterPlotData,
    lineChartData,
    scatterPlotData
  );

  if (loading) {
    return <div>Loading data...</div>;
  }

  if (error) {
    return <div>Error: {error}</div>;
  }

  return (
    <div className="app">
      <div
        style={{
          position: "sticky",
          top: 0,
          backgroundColor: "white",
          zIndex: 100,
          padding: "20px 0",
          borderBottom: "1px solid #e9ecef",
        }}
      >
        <h1>Car Rental Analytics Dashboard</h1>
      </div>

      <div
        style={{
          display: "flex",
          gap: "24px",
          minHeight: "100vh",
          padding: "20px 0",
        }}
      >
        {/* Left Sidebar - Filters */}
        <div
          style={{
            width: "320px",
            flexShrink: 0,
            position: "sticky",
            top: "20px",
            height: "fit-content",
          }}
        >
          <FilterPanel
            companies={companies}
            allFuelTypes={allFuelTypes}
            selectedCompanies={selectedCompanies}
            setSelectedCompanies={setSelectedCompanies}
            dateFrom={dateFrom}
            setDateFrom={setDateFrom}
            dateTo={dateTo}
            setDateTo={setDateTo}
            selectedFuelType={selectedFuelType}
            setSelectedFuelType={setSelectedFuelType}
            horsepowerRange={horsepowerRange}
            setHorsepowerRange={setHorsepowerRange}
            resetFilters={resetFilters}
            mantineLineData={mantineLineData}
            mantineScatterData={mantineScatterData}
          />
        </div>

        {/* Right Content - Charts */}
        <div style={{ flex: 1, minWidth: 0, border: "1px solid red" }}>
          <PriceLineChart
            mantineLineData={mantineLineData}
            getCompanyColor={getCompanyColor}
          />

          <div style={{ marginTop: "40px" }}>
            <CarPerformanceScatterChart
              mantineScatterData={mantineScatterData}
            />
          </div>

          {/* <div style={{ marginTop: "40px" }}>
            <SimpleMap />
          </div> */}
        </div>
      </div>
    </div>
  );
};
