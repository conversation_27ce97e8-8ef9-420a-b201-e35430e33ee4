import { useState, useMemo } from "react";

export const useFilteredData = (lineChartData, scatterPlotData) => {
  // Filter states
  const [selectedCompanies, setSelectedCompanies] = useState([]);
  const [selectedDateRange, setSelectedDateRange] = useState("all");
  const [selectedFuelType, setSelectedFuelType] = useState("all");
  const [horsepowerRange, setHorsepowerRange] = useState("all");

  // In-memory filtering functions
  const filteredLineChartData = useMemo(() => {
    if (!lineChartData.length) return [];

    let filtered = lineChartData;

    // Filter by companies
    if (selectedCompanies.length > 0) {
      filtered = filtered.filter((item) =>
        selectedCompanies.includes(item.company)
      );
    }

    // Filter by date range
    if (selectedDateRange !== "all") {
      const now = new Date();
      const cutoffDate = new Date();

      switch (selectedDateRange) {
        case "last3months":
          cutoffDate.setMonth(now.getMonth() - 3);
          break;
        case "last6months":
          cutoffDate.setMonth(now.getMonth() - 6);
          break;
        case "lastyear":
          cutoffDate.setFullYear(now.getFullYear() - 1);
          break;
      }

      filtered = filtered.filter((item) => new Date(item.time) >= cutoffDate);
    }

    return filtered;
  }, [lineChartData, selectedCompanies, selectedDateRange]);

  const filteredScatterPlotData = useMemo(() => {
    if (!scatterPlotData.length) return [];

    let filtered = scatterPlotData;

    // Filter by companies
    if (selectedCompanies.length > 0) {
      filtered = filtered.filter((item) =>
        selectedCompanies.includes(item.company)
      );
    }

    // Filter by fuel type
    if (selectedFuelType !== "all") {
      filtered = filtered.filter((item) => item.fuel_type === selectedFuelType);
    }

    // Filter by horsepower range
    if (horsepowerRange !== "all") {
      switch (horsepowerRange) {
        case "low":
          filtered = filtered.filter((item) => item.horsepower <= 100);
          break;
        case "medium":
          filtered = filtered.filter(
            (item) => item.horsepower > 100 && item.horsepower <= 200
          );
          break;
        case "high":
          filtered = filtered.filter((item) => item.horsepower > 200);
          break;
      }
    }

    return filtered;
  }, [scatterPlotData, selectedCompanies, selectedFuelType, horsepowerRange]);

  const resetFilters = () => {
    setSelectedCompanies([]);
    setSelectedDateRange("all");
    setSelectedFuelType("all");
    setHorsepowerRange("all");
  };

  return {
    // Filter states
    selectedCompanies,
    setSelectedCompanies,
    selectedDateRange,
    setSelectedDateRange,
    selectedFuelType,
    setSelectedFuelType,
    horsepowerRange,
    setHorsepowerRange,

    // Filtered data (computed in-memory)
    filteredLineChartData,
    filteredScatterPlotData,

    // Actions
    resetFilters,
  };
};
