import {
  MultiSelect,
  Select,
  Group,
  Text,
  Paper,
  Stack,
  Button,
} from "@mantine/core";

export const FilterPanel = ({
  companies,
  allFuelTypes,
  selectedCompanies,
  setSelectedCompanies,
  selectedDateRange,
  setSelectedDateRange,
  selectedFuelType,
  setSelectedFuelType,
  horsepowerRange,
  setHorsepowerRange,
  resetFilters,
  mantineLineData,
  mantineScatterData,
}) => {
  return (
    <Paper p="md" mb="xl" withBorder>
      <Text size="lg" fw={600} mb="md">
        Filters
      </Text>
      <Stack gap="md">
        <Group grow>
          <MultiSelect
            label="Companies"
            placeholder="Select companies"
            data={companies}
            value={selectedCompanies}
            onChange={setSelectedCompanies}
            clearable
          />
          <Select
            label="Date Range"
            placeholder="Select date range"
            data={[
              { value: "all", label: "All Time" },
              { value: "last3months", label: "Last 3 Months" },
              { value: "last6months", label: "Last 6 Months" },
              { value: "lastyear", label: "Last Year" },
            ]}
            value={selectedDateRange}
            onChange={setSelectedDateRange}
          />
        </Group>
        <Group grow>
          <Select
            label="Fuel Type"
            placeholder="Select fuel type"
            data={[
              { value: "all", label: "All Fuel Types" },
              ...allFuelTypes.map((type) => ({ value: type, label: type })),
            ]}
            value={selectedFuelType}
            onChange={setSelectedFuelType}
          />
          <Select
            label="Horsepower Range"
            placeholder="Select horsepower range"
            data={[
              { value: "all", label: "All Ranges" },
              { value: "low", label: "Low (≤100 HP)" },
              { value: "medium", label: "Medium (101-200 HP)" },
              { value: "high", label: "High (>200 HP)" },
            ]}
            value={horsepowerRange}
            onChange={setHorsepowerRange}
          />
        </Group>
        <Group>
          <Button variant="outline" onClick={resetFilters}>
            Reset All Filters
          </Button>
          <Text size="sm" c="dimmed">
            Showing {mantineLineData.length} time periods and{" "}
            {mantineScatterData.reduce(
              (acc, series) => acc + series.data.length,
              0
            )}{" "}
            cars
          </Text>
        </Group>
      </Stack>
    </Paper>
  );
};
