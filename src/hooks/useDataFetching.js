import { useState, useEffect } from "react";

export const useDataFetching = () => {
  const [lineChartData, setLineChartData] = useState([]);
  const [scatterPlotData, setScatterPlotData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Simulate backend API calls to fetch JSON data
  const fetchLineChartData = async () => {
    try {
      const response = await fetch("/data/lineChart.json");
      if (!response.ok) {
        throw new Error("Failed to fetch line chart data");
      }
      const data = await response.json();
      return data;
    } catch (err) {
      throw new Error(`Line chart data error: ${err.message}`);
    }
  };

  const fetchScatterPlotData = async () => {
    try {
      const response = await fetch("/data/scatterPlot.json");
      if (!response.ok) {
        throw new Error("Failed to fetch scatter plot data");
      }
      const data = await response.json();
      return data;
    } catch (err) {
      throw new Error(`Scatter plot data error: ${err.message}`);
    }
  };

  // Fetch both datasets on component mount
  useEffect(() => {
    const fetchAllData = async () => {
      try {
        setLoading(true);
        const [lineData, scatterData] = await Promise.all([
          fetchLineChartData(),
          fetchScatterPlotData(),
        ]);

        setTimeout(() => {
          setLineChartData(lineData);
          setScatterPlotData(scatterData);
        }, 3000);
        setError(null);
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchAllData();
  }, []);

  return {
    lineChartData,
    scatterPlotData,
    loading,
    error,
    setError,
  };
};
