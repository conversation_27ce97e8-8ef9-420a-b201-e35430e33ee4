import { useState } from "react";
import { Line<PERSON>hart } from "@mantine/charts";
import { LoadingOverlay } from "./LoadingOverlay";

export const PriceLineChart = ({ 
  mantineLineData, 
  getCompanyColor, 
  isFilterLoading 
}) => {
  const [isAnimated, setIsAnimated] = useState(false);

  return (
    <div style={{ marginBottom: "40px" }}>
      <h2>Price Trends Over Time (Mantine)</h2>

      <div style={{ position: "relative" }}>
        <div
          style={{
            "--line-animation": "draw-line 2s ease-out forwards",
            opacity: isFilterLoading ? 0.3 : 1,
            transition: "opacity 0.3s ease",
            filter: isFilterLoading ? "blur(1px)" : "none",
          }}
          onAnimationEnd={() => setIsAnimated(true)}
        >
          <style jsx>{`
            @keyframes draw-line {
              from {
                stroke-dasharray: 1000;
                stroke-dashoffset: 1000;
              }
              to {
                stroke-dasharray: 1000;
                stroke-dashoffset: 0;
              }
            }
            .recharts-line-curve {
              animation: var(--line-animation);
            }
          `}</style>

          <LineChart
            h={400}
            data={mantineLineData}
            dataKey="date"
            series={(() => {
              // Get companies that actually exist in the current data
              const companiesInData =
                mantineLineData.length > 0
                  ? Object.keys(mantineLineData[0]).filter(
                      (key) => key !== "date"
                    )
                  : [];
              return companiesInData.map((company) => ({
                name: company,
                color: getCompanyColor(company),
              }));
            })()}
            curveType="linear"
            withLegend
            withTooltip
            withDots={isAnimated}
          />
        </div>

        {/* Loading Overlay */}
        {isFilterLoading && (
          <LoadingOverlay
            message="Loading price trends..."
            subMessage="Fetching filtered data"
          />
        )}
      </div>
    </div>
  );
};
