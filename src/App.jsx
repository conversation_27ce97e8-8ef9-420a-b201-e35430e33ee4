import { useState, useEffect } from "react";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  TimeScale,
} from "chart.js";
import { Line, Scatter } from "react-chartjs-2";
import "chartjs-adapter-date-fns";
import "./App.css";

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  TimeScale
);

const App = () => {
  const [lineChartData, setLineChartData] = useState([]);
  const [scatterPlotData, setScatterPlotData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Simulate backend API calls to fetch JSON data
  const fetchLineChartData = async () => {
    try {
      const response = await fetch("/data/lineChart.json");
      if (!response.ok) {
        throw new Error("Failed to fetch line chart data");
      }
      const data = await response.json();
      return data;
    } catch (err) {
      throw new Error(`Line chart data error: ${err.message}`);
    }
  };

  const fetchScatterPlotData = async () => {
    try {
      const response = await fetch("/data/scatterPlot.json");
      if (!response.ok) {
        throw new Error("Failed to fetch scatter plot data");
      }
      const data = await response.json();
      return data;
    } catch (err) {
      throw new Error(`Scatter plot data error: ${err.message}`);
    }
  };

  // Fetch both datasets on component mount
  useEffect(() => {
    const fetchAllData = async () => {
      try {
        setLoading(true);
        const [lineData, scatterData] = await Promise.all([
          fetchLineChartData(),
          fetchScatterPlotData(),
        ]);

        setLineChartData(lineData);
        setScatterPlotData(scatterData);
        setError(null);
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchAllData();
  }, []);

  if (loading) {
    return <div>Loading data...</div>;
  }

  if (error) {
    return <div>Error: {error}</div>;
  }

  // Process line chart data for Chart.js
  // const processLineChartData = () => {
  //   if (!lineChartData.length) return null;

  //   // Group data by company
  //   const companies = [...new Set(lineChartData.map((item) => item.company))];
  //   const colors = ["#FF6384", "#36A2EB", "#FFCE56", "#4BC0C0", "#9966FF"];

  //   // Get unique sorted dates for labels
  //   const uniqueDates = [
  //     ...new Set(lineChartData.map((item) => item.time)),
  //   ].sort((a, b) => new Date(a) - new Date(b));

  //   const datasets = companies.map((company, index) => {
  //     const companyData = uniqueDates.map((date) => {
  //       const dataPoint = lineChartData.find(
  //         (item) => item.company === company && item.time === date
  //       );
  //       return dataPoint ? dataPoint.price : null;
  //     });

  //     return {
  //       label: company,
  //       data: companyData,
  //       borderColor: colors[index % colors.length],
  //       backgroundColor: colors[index % colors.length] + "20",
  //       tension: 0.1,
  //       spanGaps: true,
  //     };
  //   });

  //   return {
  //     labels: uniqueDates.map((date) => new Date(date).toLocaleDateString()),
  //     datasets,
  //   };
  // };

  // Process scatter plot data for Chart.js
  const processScatterPlotData = () => {
    if (!scatterPlotData.length) return null;

    // Group data by company
    const companies = [...new Set(scatterPlotData.map((item) => item.company))];
    const colors = ["#FF6384", "#36A2EB", "#FFCE56", "#4BC0C0", "#9966FF"];

    const datasets = companies.map((company, index) => {
      const companyData = scatterPlotData
        .filter((item) => item.company === company)
        .map((item) => ({
          x: item.horsepower,
          y: item.range,
        }));

      return {
        label: company,
        data: companyData,
        backgroundColor: colors[index % colors.length],
        borderColor: colors[index % colors.length],
        pointRadius: 6,
      };
    });

    return {
      datasets,
    };
  };

  // const lineOptions = {
  //   responsive: true,
  //   maintainAspectRatio: false,
  //   plugins: {
  //     legend: {
  //       position: "top",
  //     },
  //     title: {
  //       display: true,
  //       text: "Car Rental Prices Over Time by Company",
  //     },
  //   },
  //   scales: {
  //     x: {
  //       title: {
  //         display: true,
  //         text: "Date",
  //       },
  //     },
  //     y: {
  //       title: {
  //         display: true,
  //         text: "Price ($)",
  //       },
  //     },
  //   },
  // };

  // const scatterOptions = {
  //   responsive: true,
  //   maintainAspectRatio: false,
  //   plugins: {
  //     legend: {
  //       position: "top",
  //     },
  //     title: {
  //       display: true,
  //       text: "Car Horsepower vs Range by Company",
  //     },
  //   },
  //   scales: {
  //     x: {
  //       title: {
  //         display: true,
  //         text: "Horsepower",
  //       },
  //     },
  //     y: {
  //       title: {
  //         display: true,
  //         text: "Range (km)",
  //       },
  //     },
  //   },
  // };

  // const lineData = processLineChartData();
  // const scatterData = processScatterPlotData();

  return (
    <div className="app">
      <h1>Car Rental Analytics Dashboard</h1>

      {/* <div style={{ marginBottom: "40px" }}>
        <h2>Price Trends Over Time</h2>
        {lineData ? (
          <div style={{ height: "400px", marginBottom: "20px" }}>
            <Line data={lineData} options={lineOptions} />
          </div>
        ) : (
          <p>Loading line chart...</p>
        )}
      </div>

      <div>
        <h2>Car Performance Analysis</h2>

        {scatterData ? (
          <div style={{ height: "400px" }}>
            <Scatter data={scatterData} options={scatterOptions} />
          </div>
        ) : (
          <p>Loading scatter plot...</p>
        )}
      </div> */}
    </div>
  );
};

export default App;
