import "@mantine/core/styles.css";
import "@mantine/charts/styles.css";
import "./App.css";
import { useState, useEffect } from "react";
import { Line<PERSON><PERSON>, ScatterChart } from "@mantine/charts";
import {
  MultiSelect,
  Select,
  Group,
  Text,
  Paper,
  Stack,
  Button,
} from "@mantine/core";

const App = () => {
  const [lineChartData, setLineChartData] = useState([]);
  const [scatterPlotData, setScatterPlotData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Filter states
  const [selectedCompanies, setSelectedCompanies] = useState([]);
  const [selectedDateRange, setSelectedDateRange] = useState("all");
  const [selectedFuelType, setSelectedFuelType] = useState("all");
  const [horsepowerRange, setHorsepowerRange] = useState("all");

  // Filtered data states (simulating backend responses)
  const [filteredLineChartData, setFilteredLineChartData] = useState([]);
  const [filteredScatterPlotData, setFilteredScatterPlotData] = useState([]);
  const [isFilterLoading, setIsFilterLoading] = useState(false);

  const [isAnimated, setIsAnimated] = useState(false);

  // Simulate backend API calls to fetch filtered data
  const fetchFilteredLineChartData = async (filters) => {
    try {
      setIsFilterLoading(true);

      // Simulate API delay
      await new Promise((resolve) => setTimeout(resolve, 500));

      // Simulate building query parameters
      const queryParams = new URLSearchParams();
      if (filters.companies && filters.companies.length > 0) {
        queryParams.append("companies", filters.companies.join(","));
      }
      if (filters.dateRange && filters.dateRange !== "all") {
        queryParams.append("dateRange", filters.dateRange);
      }

      console.log(
        `🔄 Simulated API call: /api/line-chart-data?${queryParams.toString()}`
      );

      // Fetch original data and apply filters (simulating backend filtering)
      const response = await fetch("/data/lineChart.json");
      if (!response.ok) {
        throw new Error("Failed to fetch filtered line chart data");
      }
      let data = await response.json();

      // Apply filters (simulating backend logic)
      if (filters.companies && filters.companies.length > 0) {
        data = data.filter((item) => filters.companies.includes(item.company));
      }

      if (filters.dateRange && filters.dateRange !== "all") {
        const now = new Date();
        const cutoffDate = new Date();

        switch (filters.dateRange) {
          case "last3months":
            cutoffDate.setMonth(now.getMonth() - 3);
            break;
          case "last6months":
            cutoffDate.setMonth(now.getMonth() - 6);
            break;
          case "lastyear":
            cutoffDate.setFullYear(now.getFullYear() - 1);
            break;
        }

        data = data.filter((item) => new Date(item.time) >= cutoffDate);
      }

      return data;
    } catch (err) {
      throw new Error(`Filtered line chart data error: ${err.message}`);
    } finally {
      setIsFilterLoading(false);
    }
  };

  const fetchFilteredScatterPlotData = async (filters) => {
    try {
      setIsFilterLoading(true);

      // Simulate API delay
      await new Promise((resolve) => setTimeout(resolve, 300));

      // Simulate building query parameters
      const queryParams = new URLSearchParams();
      if (filters.companies && filters.companies.length > 0) {
        queryParams.append("companies", filters.companies.join(","));
      }
      if (filters.fuelType && filters.fuelType !== "all") {
        queryParams.append("fuelType", filters.fuelType);
      }
      if (filters.horsepowerRange && filters.horsepowerRange !== "all") {
        queryParams.append("horsepowerRange", filters.horsepowerRange);
      }

      console.log(
        `🔄 Simulated API call: /api/scatter-plot-data?${queryParams.toString()}`
      );

      // Fetch original data and apply filters (simulating backend filtering)
      const response = await fetch("/data/scatterPlot.json");
      if (!response.ok) {
        throw new Error("Failed to fetch filtered scatter plot data");
      }
      let data = await response.json();

      // Apply filters (simulating backend logic)
      if (filters.companies && filters.companies.length > 0) {
        data = data.filter((item) => filters.companies.includes(item.company));
      }

      if (filters.fuelType && filters.fuelType !== "all") {
        data = data.filter((item) => item.fuel_type === filters.fuelType);
      }

      if (filters.horsepowerRange && filters.horsepowerRange !== "all") {
        switch (filters.horsepowerRange) {
          case "low":
            data = data.filter((item) => item.horsepower <= 100);
            break;
          case "medium":
            data = data.filter(
              (item) => item.horsepower > 100 && item.horsepower <= 200
            );
            break;
          case "high":
            data = data.filter((item) => item.horsepower > 200);
            break;
        }
      }

      return data;
    } catch (err) {
      throw new Error(`Filtered scatter plot data error: ${err.message}`);
    } finally {
      setIsFilterLoading(false);
    }
  };

  // Simulate backend API calls to fetch JSON data
  const fetchLineChartData = async () => {
    try {
      const response = await fetch("/data/lineChart.json");
      if (!response.ok) {
        throw new Error("Failed to fetch line chart data");
      }
      const data = await response.json();
      return data;
    } catch (err) {
      throw new Error(`Line chart data error: ${err.message}`);
    }
  };

  const fetchScatterPlotData = async () => {
    try {
      const response = await fetch("/data/scatterPlot.json");
      if (!response.ok) {
        throw new Error("Failed to fetch scatter plot data");
      }
      const data = await response.json();
      return data;
    } catch (err) {
      throw new Error(`Scatter plot data error: ${err.message}`);
    }
  };

  // Fetch both datasets on component mount
  useEffect(() => {
    const fetchAllData = async () => {
      try {
        setLoading(true);
        const [lineData, scatterData] = await Promise.all([
          fetchLineChartData(),
          fetchScatterPlotData(),
        ]);

        setTimeout(() => {
          setLineChartData(lineData);
          setScatterPlotData(scatterData);

          // Initialize filtered data with all data
          setFilteredLineChartData(lineData);
          setFilteredScatterPlotData(scatterData);
        }, 3000);
        setError(null);
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchAllData();
  }, []);

  // Fetch filtered data whenever filters change (simulating backend calls)
  useEffect(() => {
    const fetchFilteredData = async () => {
      try {
        const filters = {
          companies: selectedCompanies,
          dateRange: selectedDateRange,
          fuelType: selectedFuelType,
          horsepowerRange: horsepowerRange,
        };

        console.log("🔄 Filter changed, making new API calls...", filters);

        // Only fetch if we have initial data
        if (lineChartData.length > 0 || scatterPlotData.length > 0) {
          const [filteredLineData, filteredScatterData] = await Promise.all([
            fetchFilteredLineChartData(filters),
            fetchFilteredScatterPlotData(filters),
          ]);

          setFilteredLineChartData(filteredLineData);
          setFilteredScatterPlotData(filteredScatterData);
        }
      } catch (err) {
        setError(err.message);
      }
    };

    // Don't fetch on initial render (handled by first useEffect)
    if (lineChartData.length > 0 || scatterPlotData.length > 0) {
      fetchFilteredData();
    }
  }, [selectedCompanies, selectedDateRange, selectedFuelType, horsepowerRange]);

  if (loading) {
    return <div>Loading data...</div>;
  }

  if (error) {
    return <div>Error: {error}</div>;
  }

  // Helper function to get consistent colors for companies
  const getCompanyColor = (company) => {
    const colorMap = {
      Avis: "#FF6384",
      FlexCar: "#36A2EB",
      Hertz: "#FFCE56",
      Instacar: "#4BC0C0",
      Sixt: "#9966FF",
    };
    return colorMap[company] || "#999999";
  };

  // Process filtered data for charts (using backend-filtered data)
  let mantineLineData = [];
  let mantineScatterData = [];
  let companies = [];
  let allFuelTypes = [];

  try {
    // Use the filtered data from "backend" calls
    mantineLineData = filteredLineChartData.length
      ? processMantineLineDataWithFiltered(filteredLineChartData)
      : [];
    mantineScatterData = filteredScatterPlotData.length
      ? processMantineScatterDataWithFiltered(filteredScatterPlotData)
      : [];

    // Get all available options from original data for filter dropdowns
    companies = [...new Set(lineChartData.map((item) => item.company))];
    allFuelTypes = [...new Set(scatterPlotData.map((item) => item.fuel_type))];
  } catch (err) {
    console.error("Error processing data:", err);
    setError("Error processing chart data: " + err.message);
  }

  // Helper functions for filtered data
  function processMantineLineDataWithFiltered(data) {
    if (!data || data.length === 0) return [];

    const uniqueDates = [...new Set(data.map((item) => item.time))].sort(
      (a, b) => new Date(a) - new Date(b)
    );

    // Get companies that actually exist in the filtered data
    const companiesInData = [...new Set(data.map((item) => item.company))];

    return uniqueDates.map((date) => {
      const dataPoint = { date: new Date(date).toLocaleDateString() };

      companiesInData.forEach((company) => {
        const companyData = data.find(
          (item) => item.company === company && item.time === date
        );
        dataPoint[company] = companyData ? companyData.price : null;
      });

      return dataPoint;
    });
  }

  function processMantineScatterDataWithFiltered(data) {
    const activeCompanies = [...new Set(data.map((item) => item.company))];

    return activeCompanies.map((company) => {
      const companyData = data
        .filter((item) => item.company === company)
        .map((item) => ({
          horsepower: item.horsepower,
          range: item.range,
        }));

      return {
        color: getCompanyColor(company),
        name: company,
        data: companyData,
      };
    });
  }

  const resetFilters = () => {
    setSelectedCompanies([]);
    setSelectedDateRange("all");
    setSelectedFuelType("all");
    setHorsepowerRange("all");
  };

  return (
    <div className="app">
      <h1>Car Rental Analytics Dashboard</h1>

      {/* Filters Section */}
      <Paper p="md" mb="xl" withBorder>
        <Text size="lg" fw={600} mb="md">
          Filters
        </Text>

        <Stack gap="md">
          <Group grow>
            <MultiSelect
              label="Companies"
              placeholder="Select companies"
              data={companies}
              value={selectedCompanies}
              onChange={setSelectedCompanies}
              clearable
            />

            <Select
              label="Date Range"
              placeholder="Select date range"
              data={[
                { value: "all", label: "All Time" },
                { value: "last3months", label: "Last 3 Months" },
                { value: "last6months", label: "Last 6 Months" },
                { value: "lastyear", label: "Last Year" },
              ]}
              value={selectedDateRange}
              onChange={setSelectedDateRange}
            />
          </Group>

          <Group grow>
            <Select
              label="Fuel Type"
              placeholder="Select fuel type"
              data={[
                { value: "all", label: "All Fuel Types" },
                ...allFuelTypes.map((type) => ({ value: type, label: type })),
              ]}
              value={selectedFuelType}
              onChange={setSelectedFuelType}
            />
            <Select
              label="Horsepower Range"
              placeholder="Select horsepower range"
              data={[
                { value: "all", label: "All Ranges" },
                { value: "low", label: "Low (≤100 HP)" },
                { value: "medium", label: "Medium (101-200 HP)" },
                { value: "high", label: "High (>200 HP)" },
              ]}
              value={horsepowerRange}
              onChange={setHorsepowerRange}
            />
          </Group>

          <Group>
            <Button variant="outline" onClick={resetFilters}>
              Reset All Filters
            </Button>
            <Text size="sm" c="dimmed">
              {isFilterLoading ? (
                "🔄 Loading filtered data..."
              ) : (
                <>
                  Showing {mantineLineData.length} time periods and{" "}
                  {mantineScatterData.reduce(
                    (acc, series) => acc + series.data.length,
                    0
                  )}{" "}
                  cars
                </>
              )}
            </Text>
          </Group>
        </Stack>
      </Paper>

      {/* Debug Information */}
      {/* <Paper p="md" mb="xl" withBorder>
        <Text size="lg" fw={600} mb="md">
          Debug Information
        </Text>
        <Stack gap="sm">
          <Text size="sm">Loading: {loading.toString()}</Text>
          <Text size="sm">Error: {error || "None"}</Text>
          <Text size="sm">Line Chart Data Length: {lineChartData.length}</Text>
          <Text size="sm">
            Scatter Plot Data Length: {scatterPlotData.length}
          </Text>
          <Text size="sm">
            Filtered Line Data Length: {filteredLineData.length}
          </Text>
          <Text size="sm">
            Filtered Scatter Data Length: {filteredScatterData.length}
          </Text>
          <Text size="sm">
            Mantine Line Data Length: {mantineLineData.length}
          </Text>
          <Text size="sm">
            Mantine Scatter Data Length: {mantineScatterData.length}
          </Text>
          <Text size="sm">Companies: {companies.join(", ")}</Text>
          <Text size="sm">All Fuel Types: {allFuelTypes.join(", ")}</Text>
          <Text size="sm">
            Selected Companies: {selectedCompanies.join(", ") || "None"}
          </Text>
          <Text size="sm">Selected Date Range: {selectedDateRange}</Text>
          <Text size="sm">Selected Fuel Type: {selectedFuelType}</Text>
          <Text size="sm">Selected Horsepower Range: {horsepowerRange}</Text>
        </Stack>
      </Paper> */}

      <div style={{ marginBottom: "40px" }}>
        <h2>Price Trends Over Time (Mantine)</h2>

        <div
          style={{
            "--line-animation": "draw-line 2s ease-out forwards",
          }}
          onAnimationEnd={() => setIsAnimated(true)}
        >
          <style jsx>{`
            @keyframes draw-line {
              from {
                stroke-dasharray: 1000;
                stroke-dashoffset: 1000;
              }
              to {
                stroke-dasharray: 1000;
                stroke-dashoffset: 0;
              }
            }
            .recharts-line-curve {
              animation: var(--line-animation);
            }
          `}</style>

          <LineChart
            h={400}
            data={mantineLineData}
            dataKey="date"
            series={(() => {
              // Get companies that actually exist in the current data
              const companiesInData =
                mantineLineData.length > 0
                  ? Object.keys(mantineLineData[0]).filter(
                      (key) => key !== "date"
                    )
                  : [];
              return companiesInData.map((company) => ({
                name: company,
                color: getCompanyColor(company),
              }));
            })()}
            curveType="linear"
            withLegend
            withTooltip
            withDots={isAnimated}
          />
        </div>
      </div>

      <br />

      <br />

      <div>
        <h2>Car Performance Analysis (Mantine)</h2>

        {mantineScatterData.length > 0 ? (
          <ScatterChart
            h={400}
            data={mantineScatterData}
            dataKey={{ x: "horsepower", y: "range" }}
            withLegend
            withTooltip
            xAxisLabel="Horsepower"
            yAxisLabel="Range (km)"
          />
        ) : (
          <p>Loading scatter plot...</p>
        )}
      </div>
    </div>
  );
};

export default App;
