import { useDataFetching } from "../hooks/useDataFetching";
import { useFilteredData } from "../hooks/useFilteredData";
import { useChartData } from "../hooks/useChartData";
import { FilterPanel } from "./FilterPanel";
import { PriceLineChart } from "./PriceLineChart";
import { CarPerformanceScatterChart } from "./CarPerformanceScatterChart";

export const Dashboard = () => {
  // Fetch initial data
  const { lineChartData, scatterPlotData, loading, error } = useDataFetching();

  // Handle filtered data and filter states
  const {
    selectedCompanies,
    setSelectedCompanies,
    selectedDateRange,
    setSelectedDateRange,
    selectedFuelType,
    setSelectedFuelType,
    horsepowerRange,
    setHorsepowerRange,
    filteredLineChartData,
    filteredScatterPlotData,
    isFilterLoading,
    resetFilters,
  } = useFilteredData(lineChartData, scatterPlotData);

  // Process data for charts
  const {
    mantineLineData,
    mantineScatterData,
    companies,
    allFuelTypes,
    getCompanyColor,
  } = useChartData(
    filteredLineChartData,
    filteredScatterPlotData,
    lineChartData,
    scatterPlotData
  );

  if (loading) {
    return <div>Loading data...</div>;
  }

  if (error) {
    return <div>Error: {error}</div>;
  }

  return (
    <div className="app">
      <h1>Car Rental Analytics Dashboard</h1>

      <FilterPanel
        companies={companies}
        allFuelTypes={allFuelTypes}
        selectedCompanies={selectedCompanies}
        setSelectedCompanies={setSelectedCompanies}
        selectedDateRange={selectedDateRange}
        setSelectedDateRange={setSelectedDateRange}
        selectedFuelType={selectedFuelType}
        setSelectedFuelType={setSelectedFuelType}
        horsepowerRange={horsepowerRange}
        setHorsepowerRange={setHorsepowerRange}
        resetFilters={resetFilters}
        mantineLineData={mantineLineData}
        mantineScatterData={mantineScatterData}
        isFilterLoading={isFilterLoading}
      />

      <PriceLineChart
        mantineLineData={mantineLineData}
        getCompanyColor={getCompanyColor}
        isFilterLoading={isFilterLoading}
      />

      <br />
      <br />

      <CarPerformanceScatterChart
        mantineScatterData={mantineScatterData}
        isFilterLoading={isFilterLoading}
      />
    </div>
  );
};
