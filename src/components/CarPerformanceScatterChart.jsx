import { Scatter<PERSON><PERSON> } from "@mantine/charts";
import { Paper, Center, Text } from "@mantine/core";

export const CarPerformanceScatterChart = ({ mantineScatterData }) => {
  return (
    <div>
      <h2>Car Performance Analysis (Mantine)</h2>

      {mantineScatterData.length > 0 ? (
        <ScatterChart
          h={400}
          data={mantineScatterData}
          dataKey={{ x: "horsepower", y: "range" }}
          withLegend
          withTooltip
          xAxisLabel="Horsepower"
          yAxisLabel="Range (km)"
        />
      ) : (
        <Paper p="xl" withBorder style={{ height: "400px" }}>
          <Center style={{ height: "100%" }}>
            <Text size="lg" c="dimmed">
              No data available for the selected filters
            </Text>
          </Center>
        </Paper>
      )}
    </div>
  );
};
