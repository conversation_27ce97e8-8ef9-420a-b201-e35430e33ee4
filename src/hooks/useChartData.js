import { useMemo } from "react";

export const useChartData = (filteredLineChartData, filteredScatterPlotData, lineChartData, scatterPlotData) => {
  // Helper function to get consistent colors for companies
  const getCompanyColor = (company) => {
    const colorMap = {
      Avis: "#FF6384",
      FlexCar: "#36A2EB",
      Hertz: "#FFCE56",
      Instacar: "#4BC0C0",
      Sixt: "#9966FF",
    };
    return colorMap[company] || "#999999";
  };

  // Helper functions for filtered data
  const processMantineLineDataWithFiltered = (data) => {
    if (!data || data.length === 0) return [];
    
    const uniqueDates = [...new Set(data.map((item) => item.time))].sort(
      (a, b) => new Date(a) - new Date(b)
    );

    // Get companies that actually exist in the filtered data
    const companiesInData = [...new Set(data.map((item) => item.company))];

    return uniqueDates.map((date) => {
      const dataPoint = { date: new Date(date).toLocaleDateString() };

      companiesInData.forEach((company) => {
        const companyData = data.find(
          (item) => item.company === company && item.time === date
        );
        dataPoint[company] = companyData ? companyData.price : null;
      });

      return dataPoint;
    });
  };

  const processMantineScatterDataWithFiltered = (data) => {
    if (!data || data.length === 0) return [];
    
    const activeCompanies = [...new Set(data.map((item) => item.company))];
    
    return activeCompanies.map((company) => {
      const companyData = data
        .filter((item) => item.company === company)
        .map((item) => ({
          horsepower: item.horsepower,
          range: item.range,
        }));

      return {
        color: getCompanyColor(company),
        name: company,
        data: companyData,
      };
    });
  };

  // Process filtered data for charts (using backend-filtered data)
  const mantineLineData = useMemo(() => {
    try {
      return filteredLineChartData.length
        ? processMantineLineDataWithFiltered(filteredLineChartData)
        : [];
    } catch (err) {
      console.error("Error processing line chart data:", err);
      return [];
    }
  }, [filteredLineChartData]);

  const mantineScatterData = useMemo(() => {
    try {
      return filteredScatterPlotData.length
        ? processMantineScatterDataWithFiltered(filteredScatterPlotData)
        : [];
    } catch (err) {
      console.error("Error processing scatter chart data:", err);
      return [];
    }
  }, [filteredScatterPlotData]);

  // Get all available options from original data for filter dropdowns
  const companies = useMemo(() => {
    return [...new Set(lineChartData.map((item) => item.company))];
  }, [lineChartData]);

  const allFuelTypes = useMemo(() => {
    return [...new Set(scatterPlotData.map((item) => item.fuel_type))];
  }, [scatterPlotData]);

  return {
    mantineLineData,
    mantineScatterData,
    companies,
    allFuelTypes,
    getCompanyColor,
  };
};
