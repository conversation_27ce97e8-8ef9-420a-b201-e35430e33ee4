import {
  MultiSelect,
  Select,
  Group,
  Text,
  Paper,
  Stack,
  Button,
  Divider,
  Badge,
} from "@mantine/core";
import { DateInput } from "@mantine/dates";

export const FilterPanel = ({
  companies,
  allFuelTypes,
  selectedCompanies,
  setSelectedCompanies,
  dateFrom,
  setDateFrom,
  dateTo,
  setDateTo,
  selectedFuelType,
  setSelectedFuelType,
  horsepowerRange,
  setHorsepowerRange,
  resetFilters,
  mantineLineData,
  mantineScatterData,
}) => {
  console.log("FilterPanel render:", { dateFrom, dateTo });
  return (
    <Paper p="md" withBorder>
      <Text size="lg" fw={600} mb="md">
        Filters
      </Text>
      <Stack gap="md">
        {/* Global Filters - Apply to both charts */}
        <div>
          <Group gap="xs" mb="xs">
            <Text size="sm" fw={500}>
              Global Filters
            </Text>
            <Badge size="xs" color="blue">
              Both Charts
            </Badge>
          </Group>
          <MultiSelect
            label="Companies"
            placeholder="Select companies"
            data={companies}
            value={selectedCompanies}
            onChange={setSelectedCompanies}
            clearable
          />
        </div>

        <Divider />

        {/* Line Chart Specific Filters */}
        <div>
          <Group gap="xs" mb="xs">
            <Text size="sm" fw={500}>
              Date Range
            </Text>
            <Badge size="xs" color="green">
              Price Trends Chart
            </Badge>
          </Group>
          <Group grow>
            <DateInput
              label="From Date"
              placeholder="Select start date"
              value={dateFrom}
              onChange={(value) => {
                console.log("From date changed:", value);
                setDateFrom(value);
              }}
              clearable
            />
            <DateInput
              label="To Date"
              placeholder="Select end date"
              value={dateTo}
              onChange={(value) => {
                console.log("To date changed:", value);
                setDateTo(value);
              }}
              clearable
            />
          </Group>
        </div>

        <Divider />

        {/* Scatter Chart Specific Filters */}
        <div>
          <Group gap="xs" mb="xs">
            <Text size="sm" fw={500}>
              Car Specifications
            </Text>
            <Badge size="xs" color="orange">
              Performance Chart
            </Badge>
          </Group>
          <Group grow>
            <Select
              label="Fuel Type"
              placeholder="Select fuel type"
              data={[
                { value: "all", label: "All Fuel Types" },
                ...allFuelTypes.map((type) => ({ value: type, label: type })),
              ]}
              value={selectedFuelType}
              onChange={setSelectedFuelType}
            />
            <Select
              label="Horsepower Range"
              placeholder="Select horsepower range"
              data={[
                { value: "all", label: "All Ranges" },
                { value: "low", label: "Low (≤100 HP)" },
                { value: "medium", label: "Medium (101-200 HP)" },
                { value: "high", label: "High (>200 HP)" },
              ]}
              value={horsepowerRange}
              onChange={setHorsepowerRange}
            />
          </Group>
        </div>

        <Divider />
        <Group>
          <Button variant="outline" onClick={resetFilters}>
            Reset All Filters
          </Button>
          <Text size="sm" c="dimmed">
            Showing {mantineLineData.length} time periods and{" "}
            {mantineScatterData.reduce(
              (acc, series) => acc + series.data.length,
              0
            )}{" "}
            cars
          </Text>
        </Group>
      </Stack>
    </Paper>
  );
};
