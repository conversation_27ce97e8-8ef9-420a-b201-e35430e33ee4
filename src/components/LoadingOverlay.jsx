import { Loader, Stack, Text } from "@mantine/core";

export const LoadingOverlay = ({ message, subMessage }) => {
  return (
    <div
      style={{
        position: "absolute",
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        backgroundColor: "rgba(255, 255, 255, 0.8)",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        zIndex: 10,
        animation: "pulse 2s infinite",
      }}
    >
      <style jsx>{`
        @keyframes pulse {
          0%,
          100% {
            opacity: 0.8;
          }
          50% {
            opacity: 0.4;
          }
        }
      `}</style>
      <Stack align="center" gap="sm">
        <Loader size="md" />
        <Text size="sm" fw={500}>
          {message}
        </Text>
        <Text size="xs" c="dimmed">
          {subMessage}
        </Text>
      </Stack>
    </div>
  );
};
