import { useState, useEffect } from "react";
import "./App.css";

const App = () => {
  const [lineChartData, setLineChartData] = useState([]);
  const [scatterPlotData, setScatterPlotData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Simulate backend API calls to fetch JSON data
  const fetchLineChartData = async () => {
    try {
      const response = await fetch("/data/lineChart.json");
      if (!response.ok) {
        throw new Error("Failed to fetch line chart data");
      }
      const data = await response.json();
      return data;
    } catch (err) {
      throw new Error(`Line chart data error: ${err.message}`);
    }
  };

  const fetchScatterPlotData = async () => {
    try {
      const response = await fetch("/data/scatterPlot.json");
      if (!response.ok) {
        throw new Error("Failed to fetch scatter plot data");
      }
      const data = await response.json();
      return data;
    } catch (err) {
      throw new Error(`Scatter plot data error: ${err.message}`);
    }
  };

  // Fetch both datasets on component mount
  useEffect(() => {
    const fetchAllData = async () => {
      try {
        setLoading(true);
        const [lineData, scatterData] = await Promise.all([
          fetchLineChartData(),
          fetchScatterPlotData(),
        ]);

        setLineChartData(lineData);
        setScatterPlotData(scatterData);
        setError(null);
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchAllData();
  }, []);

  if (loading) {
    return <div>Loading data...</div>;
  }

  if (error) {
    return <div>Error: {error}</div>;
  }

  return (
    <div className="app">
      <h1>Graphs App Data</h1>

      <section>
        <h2>Line Chart Data (Car Rental Prices)</h2>
        <p>Total records: {lineChartData.length}</p>
        <div
          style={{
            maxHeight: "300px",
            overflow: "auto",
            border: "1px solid #ccc",
            padding: "10px",
          }}
        >
          <pre>{JSON.stringify(lineChartData, null, 2)}</pre>
        </div>
      </section>

      <section style={{ marginTop: "20px" }}>
        <h2>Scatter Plot Data (Car Specifications)</h2>
        <p>Total records: {scatterPlotData.length}</p>
        <div
          style={{
            maxHeight: "300px",
            overflow: "auto",
            border: "1px solid #ccc",
            padding: "10px",
          }}
        >
          <pre>{JSON.stringify(scatterPlotData, null, 2)}</pre>
        </div>
      </section>
    </div>
  );
};

export default App;
