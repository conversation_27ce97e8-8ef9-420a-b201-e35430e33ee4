import { StrictMode } from "react";
import "@mantine/core/styles.css";
import "@mantine/charts/styles.css";
import { createRoot } from "react-dom/client";
import "./index.css";
import App from "./App.jsx";
import { MantineProvider } from "@mantine/core";
import { DatesProvider } from "@mantine/dates";

createRoot(document.getElementById("root")).render(
  <StrictMode>
    <MantineProvider>
      <DatesProvider settings={{ locale: "en" }}>
        <App />
      </DatesProvider>
    </MantineProvider>
  </StrictMode>
);
