import { useDataFetching } from "../hooks/useDataFetching";
import { useFilteredData } from "../hooks/useFilteredData";
import { useChartData } from "../hooks/useChartData";
import { FilterPanel } from "./FilterPanel";
import { PriceLineChart } from "./PriceLineChart";
import { CarPerformanceScatterChart } from "./CarPerformanceScatterChart";
import { SimpleMap } from "./SimpleMap";

export const Dashboard = () => {
  // Fetch initial data
  const { lineChartData, scatterPlotData, loading, error } = useDataFetching();

  console.log("Dashboard render:", {
    loading,
    error,
    lineChartDataLength: lineChartData.length,
  });

  // Handle filtered data and filter states
  const {
    selectedCompanies,
    setSelectedCompanies,
    dateFrom,
    setDateFrom,
    dateTo,
    setDateTo,
    selectedFuelType,
    setSelectedFuelType,
    horsepowerRange,
    setHorsepowerRange,
    filteredLineChartData,
    filteredScatterPlotData,
    resetFilters,
  } = useFilteredData(lineChartData, scatterPlotData);

  // Process data for charts
  const {
    mantineLineData,
    mantineScatterData,
    companies,
    allFuelTypes,
    getCompanyColor,
  } = useChartData(
    filteredLineChartData,
    filteredScatterPlotData,
    lineChartData,
    scatterPlotData
  );

  if (loading) {
    return <div>Loading data...</div>;
  }

  if (error) {
    return <div>Error: {error}</div>;
  }

  return (
    <div
      className="app"
      style={{ height: "100vh", display: "flex", flexDirection: "column" }}
    >
      {/* Fixed Title */}
      <div
        style={{
          backgroundColor: "white",
          zIndex: 100,
          padding: "20px",
          borderBottom: "1px solid #e9ecef",
          flexShrink: 0,
        }}
      >
        <h1 style={{ margin: 0 }}>Car Rental Analytics Dashboard</h1>
      </div>

      {/* Main Content Area */}
      <div
        style={{
          display: "flex",
          gap: "24px",
          flex: 1,
          overflow: "hidden",
        }}
      >
        {/* Fixed Left Sidebar - Filters */}
        <div
          style={{
            width: "320px",
            flexShrink: 0,
            padding: "20px",
            backgroundColor: "#f8f9fa",
            borderRight: "1px solid #e9ecef",
            overflow: "auto",
          }}
        >
          <FilterPanel
            companies={companies}
            allFuelTypes={allFuelTypes}
            selectedCompanies={selectedCompanies}
            setSelectedCompanies={setSelectedCompanies}
            dateFrom={dateFrom}
            setDateFrom={setDateFrom}
            dateTo={dateTo}
            setDateTo={setDateTo}
            selectedFuelType={selectedFuelType}
            setSelectedFuelType={setSelectedFuelType}
            horsepowerRange={horsepowerRange}
            setHorsepowerRange={setHorsepowerRange}
            resetFilters={resetFilters}
            mantineLineData={mantineLineData}
            mantineScatterData={mantineScatterData}
          />
        </div>

        {/* Scrollable Right Content - Charts */}
        <div
          style={{
            flex: "0 0 700px",
            width: "700px",
            maxWidth: "700px",
            minWidth: "700px",
            overflow: "auto",
            padding: "20px",
          }}
        >
          <PriceLineChart
            mantineLineData={mantineLineData}
            getCompanyColor={getCompanyColor}
          />

          <div style={{ marginTop: "40px" }}>
            <CarPerformanceScatterChart
              mantineScatterData={mantineScatterData}
            />
          </div>

          <div style={{ padding: "64px 0" }}>
            <SimpleMap />
          </div>
        </div>
      </div>
    </div>
  );
};
