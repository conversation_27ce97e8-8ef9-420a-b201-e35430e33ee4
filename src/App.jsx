import "@mantine/core/styles.css";
import "@mantine/charts/styles.css";
import "./App.css";
import { useState, useEffect } from "react";
import { Line<PERSON><PERSON>, ScatterChart } from "@mantine/charts";

const App = () => {
  const [lineChartData, setLineChartData] = useState([]);
  const [scatterPlotData, setScatterPlotData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const [isAnimated, setIsAnimated] = useState(false);

  // Simulate backend API calls to fetch JSON data
  const fetchLineChartData = async () => {
    try {
      const response = await fetch("/data/lineChart.json");
      if (!response.ok) {
        throw new Error("Failed to fetch line chart data");
      }
      const data = await response.json();
      return data;
    } catch (err) {
      throw new Error(`Line chart data error: ${err.message}`);
    }
  };

  const fetchScatterPlotData = async () => {
    try {
      const response = await fetch("/data/scatterPlot.json");
      if (!response.ok) {
        throw new Error("Failed to fetch scatter plot data");
      }
      const data = await response.json();
      return data;
    } catch (err) {
      throw new Error(`Scatter plot data error: ${err.message}`);
    }
  };

  // Fetch both datasets on component mount
  useEffect(() => {
    const fetchAllData = async () => {
      try {
        setLoading(true);
        const [lineData, scatterData] = await Promise.all([
          fetchLineChartData(),
          fetchScatterPlotData(),
        ]);

        setTimeout(() => {
          setLineChartData(lineData);
          setScatterPlotData(scatterData);
        }, 3000);
        setError(null);
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchAllData();
  }, []);

  if (loading) {
    return <div>Loading data...</div>;
  }

  if (error) {
    return <div>Error: {error}</div>;
  }

  // Process line chart data for Mantine
  const processMantineLineData = () => {
    if (!lineChartData.length) return [];

    // Get unique sorted dates
    const uniqueDates = [
      ...new Set(lineChartData.map((item) => item.time)),
    ].sort((a, b) => new Date(a) - new Date(b));

    // Transform data for Mantine LineChart
    return uniqueDates.map((date) => {
      const dataPoint = { date: new Date(date).toLocaleDateString() };

      // Add price for each company at this date
      const companies = [...new Set(lineChartData.map((item) => item.company))];

      companies.forEach((company) => {
        const companyData = lineChartData.find(
          (item) => item.company === company && item.time === date
        );
        dataPoint[company] = companyData ? companyData.price : null;
      });

      return dataPoint;
    });
  };

  // Helper function to get consistent colors for companies
  const getCompanyColor = (company) => {
    const colorMap = {
      Avis: "#FF6384",
      FlexCar: "#36A2EB",
      Hertz: "#FFCE56",
      Instacar: "#4BC0C0",
      Sixt: "#9966FF",
    };
    return colorMap[company] || "#999999";
  };

  // Process scatter plot data for Mantine
  const processMantineScatterData = () => {
    if (!scatterPlotData.length) return [];

    // Group data by company
    const companies = [...new Set(scatterPlotData.map((item) => item.company))];

    return companies.map((company) => {
      const companyData = scatterPlotData
        .filter((item) => item.company === company)
        .map((item) => ({
          horsepower: item.horsepower,
          range: item.range,
        }));

      return {
        color: getCompanyColor(company),
        name: company,
        data: companyData,
      };
    });
  };

  const mantineLineData = lineChartData ? processMantineLineData() : [];
  // console.log("mantineLineData", mantineLineData);

  const mantineScatterData = processMantineScatterData();
  console.log("mantineScatterData", mantineScatterData);

  const companies = [...new Set(lineChartData.map((item) => item.company))];


  return (
    <div className="app">
      <h1>Car Rental Analytics Dashboard</h1>

      <div style={{ marginBottom: "40px" }}>
        <h2>Price Trends Over Time (Mantine)</h2>

        <div
          style={{
            "--line-animation": "draw-line 2s ease-out forwards",
          }}
          onAnimationEnd={() => setIsAnimated(true)}
        >
          <style jsx>{`
            @keyframes draw-line {
              from {
                stroke-dasharray: 1000;
                stroke-dashoffset: 1000;
              }
              to {
                stroke-dasharray: 1000;
                stroke-dashoffset: 0;
              }
            }
            .recharts-line-curve {
              animation: var(--line-animation);
            }
          `}</style>

          <LineChart
            h={400}
            data={mantineLineData}
            dataKey="date"
            series={companies.map((company) => ({
              name: company,
              color: getCompanyColor(company),
            }))}
            curveType="linear"
            withLegend
            withTooltip
            withDots={isAnimated}
          />
        </div>
      </div>

      <br />

      <br />

      <div>
        <h2>Car Performance Analysis (Mantine)</h2>

        {mantineScatterData.length > 0 ? (
          <ScatterChart
            h={400}
            data={mantineScatterData}
            dataKey={{ x: "horsepower", y: "range" }}
            withLegend
            withTooltip
            xAxisLabel="Horsepower"
            yAxisLabel="Range (km)"
          />
        ) : (
          <p>Loading scatter plot...</p>
        )}
      </div>
    </div>
  );
};

export default App;
