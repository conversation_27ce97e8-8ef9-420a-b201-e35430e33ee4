import { Scatter<PERSON><PERSON> } from "@mantine/charts";
import { Paper, Center, Text } from "@mantine/core";
import { LoadingOverlay } from "./LoadingOverlay";

export const CarPerformanceScatterChart = ({ 
  mantineScatterData, 
  isFilterLoading 
}) => {
  return (
    <div>
      <h2>Car Performance Analysis (Mantine)</h2>

      <div style={{ position: "relative" }}>
        {mantineScatterData.length > 0 ? (
          <div
            style={{
              opacity: isFilterLoading ? 0.3 : 1,
              transition: "opacity 0.3s ease",
              filter: isFilterLoading ? "blur(1px)" : "none",
            }}
          >
            <ScatterChart
              h={400}
              data={mantineScatterData}
              dataKey={{ x: "horsepower", y: "range" }}
              withLegend
              withTooltip
              xAxisLabel="Horsepower"
              yAxisLabel="Range (km)"
            />
          </div>
        ) : (
          <Paper p="xl" withBorder style={{ height: "400px" }}>
            <Center style={{ height: "100%" }}>
              <Text size="lg" c="dimmed">
                No data available for the selected filters
              </Text>
            </Center>
          </Paper>
        )}

        {/* Loading Overlay */}
        {isFilterLoading && mantineScatterData.length > 0 && (
          <LoadingOverlay
            message="Loading car performance..."
            subMessage="Fetching filtered data"
          />
        )}
      </div>
    </div>
  );
};
